#!/bin/bash

# Cloudflare DNS Debugging Script
# This script helps diagnose and fix common Cloudflare timeout issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v terraform &> /dev/null; then
        missing_deps+=("terraform")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_status "Please install the missing dependencies and try again."
        exit 1
    fi
    
    print_success "All dependencies are installed"
}

# Function to test Cloudflare API connectivity
test_api_connectivity() {
    print_status "Testing Cloudflare API connectivity..."
    
    if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
        print_error "CLOUDFLARE_API_TOKEN environment variable is not set"
        return 1
    fi
    
    local response
    response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/user/tokens/verify")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "API connectivity test passed"
        echo "$body" | jq -r '.result | "Token Status: \(.status), ID: \(.id)"'
        return 0
    else
        print_error "API connectivity test failed (HTTP $http_code)"
        echo "$body" | jq -r '.errors[]?.message // "Unknown error"' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Function to check zone permissions
check_zone_permissions() {
    local zone_id="$1"
    
    if [ -z "$zone_id" ]; then
        print_warning "No zone ID provided, skipping zone permission check"
        return 0
    fi
    
    print_status "Checking zone permissions for zone: $zone_id"
    
    local response
    response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/zones/$zone_id")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "Zone access verified"
        echo "$body" | jq -r '.result | "Zone: \(.name), Status: \(.status)"'
        return 0
    else
        print_error "Zone access failed (HTTP $http_code)"
        echo "$body" | jq -r '.errors[]?.message // "Unknown error"' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Function to check current DNS records
check_dns_records() {
    local zone_id="$1"
    
    if [ -z "$zone_id" ]; then
        print_warning "No zone ID provided, skipping DNS records check"
        return 0
    fi
    
    print_status "Checking existing DNS records..."
    
    local response
    response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/zones/$zone_id/dns_records")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        local record_count
        record_count=$(echo "$body" | jq -r '.result | length')
        print_success "Found $record_count DNS records"
        
        if [ "$record_count" -gt 0 ]; then
            echo "$body" | jq -r '.result[] | "  - \(.name) (\(.type)): \(.content)"'
        fi
        return 0
    else
        print_error "Failed to fetch DNS records (HTTP $http_code)"
        echo "$body" | jq -r '.errors[]?.message // "Unknown error"' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Function to validate Terraform configuration
validate_terraform() {
    local terraform_dir="$1"
    
    if [ ! -d "$terraform_dir" ]; then
        print_error "Terraform directory not found: $terraform_dir"
        return 1
    fi
    
    print_status "Validating Terraform configuration in $terraform_dir..."
    
    cd "$terraform_dir"
    
    if terraform validate; then
        print_success "Terraform configuration is valid"
        return 0
    else
        print_error "Terraform configuration validation failed"
        return 1
    fi
}

# Function to suggest timeout fixes
suggest_timeout_fixes() {
    print_status "Timeout troubleshooting suggestions:"
    echo ""
    echo "1. Increase timeout values in your Terraform configuration:"
    echo "   record_create_timeout = \"15m\""
    echo "   record_update_timeout = \"10m\""
    echo "   record_delete_timeout = \"10m\""
    echo ""
    echo "2. Enable API logging for debugging:"
    echo "   enable_logging = true"
    echo ""
    echo "3. Check network connectivity and DNS resolution"
    echo ""
    echo "4. Verify API rate limits haven't been exceeded"
    echo ""
    echo "5. Consider running terraform apply with parallelism limit:"
    echo "   terraform apply -parallelism=1"
}

# Main function
main() {
    echo "=== Cloudflare DNS Debugging Tool ==="
    echo ""
    
    # Parse command line arguments
    local terraform_dir="${1:-./terraform/modules/cloudflare}"
    local zone_id="${2:-$CLOUDFLARE_ZONE_ID}"
    
    # Check dependencies
    check_dependencies
    echo ""
    
    # Test API connectivity
    if test_api_connectivity; then
        echo ""
        
        # Check zone permissions if zone ID provided
        if [ -n "$zone_id" ]; then
            check_zone_permissions "$zone_id"
            echo ""
            check_dns_records "$zone_id"
            echo ""
        fi
        
        # Validate Terraform configuration
        validate_terraform "$terraform_dir"
        echo ""
        
        print_success "All checks completed successfully!"
    else
        echo ""
        print_error "API connectivity test failed. Please check your API token and try again."
    fi
    
    echo ""
    suggest_timeout_fixes
}

# Show usage if help requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "Usage: $0 [terraform_dir] [zone_id]"
    echo ""
    echo "Environment variables:"
    echo "  CLOUDFLARE_API_TOKEN - Required: Your Cloudflare API token"
    echo "  CLOUDFLARE_ZONE_ID   - Optional: Zone ID to check (can be passed as argument)"
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "  $0 ./terraform/dns"
    echo "  $0 ./terraform/dns abc123def456"
    echo ""
    exit 0
fi

# Run main function
main "$@"
