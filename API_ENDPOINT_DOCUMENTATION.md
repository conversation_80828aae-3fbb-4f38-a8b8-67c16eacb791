# New Public API Endpoint: Get Services by Line Code

## Endpoint Details

**URL:** `GET /api/v1/public/services/line-code/{line_code}`

**Authentication:** API Key required (via header or query parameter)

**Description:** Retrieves all services associated with a specific order line code.

## Parameters

- `line_code` (path parameter, required): The line code of the order to retrieve services for

## Response Format

### Success Response (200 OK)
```json
{
  "status": true,
  "message": "Services retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2023-01-01T00:00:00Z",
      "update_at": "2023-01-01T00:00:00Z",
      "name": "web-service",
      "port": "80",
      "target_port": "8080",
      "type": "ClusterIP",
      "cluster_ip": "********",
      "external_ip": "",
      "is_active": true,
      "namespace": {
        "id": 1,
        "name": "production",
        "slug": "production"
      },
      "deployment": {
        "id": 1,
        "name": "web-deployment",
        "image": "nginx:latest"
      },
      "status": {
        "id": 1,
        "name": "Running",
        "color": "green"
      },
      "ingress_specs": [
        {
          "id": 1,
          "host": "example.com",
          "path": "/"
        }
      ]
    }
  ]
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "status": false,
  "message": "Line code parameter is required",
  "data": null
}
```

#### 401 Unauthorized
```json
{
  "status": false,
  "message": "API key is required",
  "data": null
}
```

#### 404 Not Found
```json
{
  "status": false,
  "message": "No services found for the given line code",
  "data": null
}
```

#### 500 Internal Server Error
```json
{
  "status": false,
  "message": "Internal server error message",
  "data": null
}
```

## Database Query Logic

The endpoint performs the following database operations:

1. **Joins across three tables:**
   - `service` table (main data)
   - `order_namespace` table (linking services to orders via namespace)
   - `order` table (contains the line_code)

2. **Query structure:**
   ```sql
   SELECT service.* 
   FROM service 
   JOIN order_namespace ON service.namespace_id = order_namespace.namespace_id
   JOIN "order" ON order_namespace.order_id = "order".id
   WHERE "order".line_code = ?
   ```

3. **Preloaded relationships:**
   - Namespace (with Cluster and Workspace)
   - Deployment
   - IngressSpecs
   - Status

## Usage Examples

### Using curl with API key in header
```bash
curl -X GET "http://localhost:8080/api/v1/public/services/line-code/LINE001" \
  -H "X-API-Key: your-api-key-here"
```

### Using curl with API key in query parameter
```bash
curl -X GET "http://localhost:8080/api/v1/public/services/line-code/LINE001?api_key=your-api-key-here"
```

## Implementation Details

### Files Modified/Created:

1. **Repository Layer** (`internal/adapters/repository/service.go`):
   - Added `FindByLineCode(lineCode string) ([]*domain.Service, error)` method

2. **Service Layer** (`internal/core/services/service.go`):
   - Added `GetByLineCode(lineCode string) ([]*domain.Service, error)` method

3. **Handler Layer** (`internal/adapters/http/handlers/service_handler.go`):
   - Added `GetServiceByLineCode(c *fiber.Ctx) error` method

4. **Routes** (`internal/adapters/http/routes/public_routes.go`):
   - Added route registration for the new endpoint

5. **Interfaces** (`internal/core/ports/service.go`):
   - Updated ServiceRepository and ServiceService interfaces

### Error Handling:

- Validates that line_code parameter is provided
- Returns appropriate HTTP status codes
- Handles database errors gracefully
- Returns empty array when no services found (with 404 status)

### Security:

- Requires API key authentication (same as other public endpoints)
- No additional authorization needed as it's a public endpoint
- Input validation on line_code parameter
