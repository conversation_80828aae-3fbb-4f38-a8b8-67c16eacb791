package handlers

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type ServiceHandler struct {
	serviceService   ports.ServiceService
	domainService    ports.DomainService
	namespaceService ports.NamespaceService
	switchDnsService ports.SwitchDnsService
	userService      ports.UserService
}

func NewServiceHandler(serviceService ports.ServiceService, domainService ports.DomainService, namespaceService ports.NamespaceService, switchDnsService ports.SwitchDnsService, userService ports.UserService,
) *ServiceHandler {
	return &ServiceHandler{
		serviceService:   serviceService,
		domainService:    domainService,
		namespaceService: namespaceService,
		switchDnsService: switchDnsService,
		userService:      userService,
	}
}

func (h *ServiceHandler) CreateService(c *fiber.Ctx) error {
	var req dto.CreateServiceRequest
	if err := c.Body<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Default is_active to true if not provided
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	service, err := h.serviceService.Create(req.Name, req.Port, req.TargetPort, req.Type, req.ClusterIP, req.ExternalIP, isActive, req.NamespaceID, req.DeploymentID)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Service created successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) GetServices(c *fiber.Ctx) error {
	filter := &ports.ServiceFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid namespace_id parameter")
		}
		filter.NamespaceID = &namespaceID
	}

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		isActive, err := strconv.ParseBool(isActiveStr)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid is_active parameter")
		}
		filter.IsActive = &isActive
	}

	services, err := h.serviceService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var serviceDTOs []*dto.ServiceListItemResponse
	for _, service := range services {
		serviceDTOs = append(serviceDTOs, dto.ToServiceListItemDTO(service))
	}

	return response.Success(c, fiber.StatusOK, "Services retrieved successfully", serviceDTOs)
}

func (h *ServiceHandler) GetServicesForDns(c *fiber.Ctx) error {
	filter := &ports.ServiceFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid namespace_id parameter")
		}
		filter.NamespaceID = &namespaceID
	}

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		isActive, err := strconv.ParseBool(isActiveStr)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid is_active parameter")
		}
		filter.IsActive = &isActive
	}

	currentDefaultDomain, err := h.domainService.GetDefaultByNamespaceID(*filter.NamespaceID)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	masterDomain := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME")
	isMasterDefault := currentDefaultDomain.Name == masterDomain

	namespace, err := h.namespaceService.GetByID(*filter.NamespaceID)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}
	namespaceID := strconv.FormatUint(namespace.ID, 10)

	services, err := h.serviceService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var serviceDTOs []*dto.ServiceListDnsResponse
	for _, service := range services {
		if service.Name == "app" {
			if isMasterDefault {
				service.Name = fmt.Sprintf("%s-%s-%s", namespace.Name, namespaceID, "app")
			} else {
				service.Name = "@"
			}
			serviceDTOs = append(serviceDTOs, dto.ToServiceListDnsDTO(service))
			if isMasterDefault {
				service.Name = fmt.Sprintf("%s-%s-%s", namespace.Name, namespaceID, "www")
			} else {
				service.Name = "www"
			}
			serviceDTOs = append(serviceDTOs, dto.ToServiceListDnsDTO(service))
		} else {
			if isMasterDefault {
				service.Name = fmt.Sprintf("%s-%s-%s", namespace.Name, namespaceID, service.Name)
			} else {
				service.Name = service.Name
			}
			serviceDTOs = append(serviceDTOs, dto.ToServiceListDnsDTO(service))
		}
	}

	return response.Success(c, fiber.StatusOK, "Services retrieved successfully", serviceDTOs)
}

func (h *ServiceHandler) GetServiceByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	service, err := h.serviceService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service retrieved successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) UpdateService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateServiceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Default is_active to true if not provided
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	service, err := h.serviceService.Update(id, req.Name, req.Port, req.TargetPort, req.Type, req.ClusterIP, req.ExternalIP, isActive, req.NamespaceID, req.DeploymentID, req.StatusID)
	if err != nil {
		fmt.Println(err)
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service updated successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) UpdateServiceStatus(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateServiceStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	service, err := h.serviceService.UpdateStatus(id, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service status updated successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) UpdateServiceActiveStatus(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	type UpdateActiveStatusRequest struct {
		IsActive bool `json:"is_active"`
	}

	var req UpdateActiveStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	service, err := h.serviceService.UpdateActiveStatus(id, req.IsActive)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	userIDUint64, err := h.domainService.GetUserIDFromNamespaceID(service.NamespaceID)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "User not found for this service: "+err.Error())
	}

	accessToken, err := h.userService.GenerateTokenFromUserID(userIDUint64)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to generate access token: "+err.Error())
	}

	_, err = h.switchDnsService.SwitchDns(userIDUint64, accessToken, service.NamespaceID)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service active status updated successfully", dto.ToServiceDetailDTO(service))
}

func (h *ServiceHandler) DeleteService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.serviceService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Service deleted successfully", nil)
}
