package repository

import (
	"testing"

	"ops-api/internal/core/domain"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&domain.Service{},
		&domain.Namespace{},
		&domain.Order{},
		&domain.OrderNamespace{},
		&domain.Cluster{},
		&domain.Deployment{},
		&domain.ServerStatus{},
	)
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	return db
}

func TestServiceRepository_FindByLineCode(t *testing.T) {
	db := setupTestDB(t)
	repo := NewServiceRepository(db)

	// Create test data
	cluster := &domain.Cluster{
		BaseModel: domain.BaseModel{ID: 1},
		Name:      "test-cluster",
		Region:    "us-east-1",
	}
	db.Create(cluster)

	namespace := &domain.Namespace{
		BaseModel: domain.BaseModel{ID: 1},
		Name:      "test-namespace",
		Slug:      "test-namespace",
		IsActive:  true,
		ClusterID: 1,
	}
	db.Create(namespace)

	order := &domain.Order{
		BaseModel:   domain.BaseModel{ID: 1},
		Name:        "Test Order",
		Code:        "TEST001",
		Description: "Test order description",
		LineCode:    "LINE001",
		IsConfirmed: true,
		UserID:      1,
		TemplateID:  1,
	}
	db.Create(order)

	orderNamespace := &domain.OrderNamespace{
		BaseModel:   domain.BaseModel{ID: 1},
		OrderID:     1,
		NamespaceID: 1,
	}
	db.Create(orderNamespace)

	status := &domain.ServerStatus{
		BaseModel: domain.BaseModel{ID: 1},
		Name:      "Running",
		Color:     "green",
	}
	db.Create(status)

	deployment := &domain.Deployment{
		BaseModel:     domain.BaseModel{ID: 1},
		Name:          "test-deployment",
		Image:         "nginx:latest",
		ContainerPort: 80,
		Replicas:      1,
		NamespaceID:   1,
		StatusID:      1,
	}
	db.Create(deployment)

	service := &domain.Service{
		BaseModel:    domain.BaseModel{ID: 1},
		Name:         "test-service",
		Port:         "80",
		TargetPort:   "8080",
		Type:         "ClusterIP",
		IsActive:     true,
		NamespaceID:  1,
		DeploymentID: 1,
		StatusID:     1,
	}
	db.Create(service)

	// Test FindByLineCode
	services, err := repo.FindByLineCode("LINE001")
	if err != nil {
		t.Fatalf("FindByLineCode failed: %v", err)
	}

	if len(services) != 1 {
		t.Fatalf("Expected 1 service, got %d", len(services))
	}

	if services[0].Name != "test-service" {
		t.Errorf("Expected service name 'test-service', got '%s'", services[0].Name)
	}

	// Test with non-existent line code
	services, err = repo.FindByLineCode("NONEXISTENT")
	if err != nil {
		t.Fatalf("FindByLineCode failed: %v", err)
	}

	if len(services) != 0 {
		t.Fatalf("Expected 0 services for non-existent line code, got %d", len(services))
	}
}
