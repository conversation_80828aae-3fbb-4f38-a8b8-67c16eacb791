package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type ServiceService struct {
	serviceRepo   ports.ServiceRepository
	namespaceRepo ports.NamespaceRepository
}

func NewServiceService(serviceRepo ports.ServiceRepository, namespaceRepo ports.NamespaceRepository) ports.ServiceService {
	return &ServiceService{
		serviceRepo:   serviceRepo,
		namespaceRepo: namespaceRepo,
	}
}

func (s *ServiceService) Create(name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID uint64) (*domain.Service, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if port == "" {
		return nil, errors.New("port is required")
	}
	if targetPort == "" {
		return nil, errors.New("target port is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}
	if deploymentID == 0 {
		return nil, errors.New("deployment ID is required")
	}

	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	service := &domain.Service{
		Name:         name,
		Port:         port,
		TargetPort:   targetPort,
		Type:         serviceType,
		ClusterIP:    clusterIp,
		ExternalIP:   ExternalIp,
		IsActive:     isActive,
		NamespaceID:  namespaceID,
		DeploymentID: deploymentID,
		StatusID:     1,
	}

	if err := s.serviceRepo.Insert(service); err != nil {
		return nil, err
	}

	return s.serviceRepo.FindByID(service.ID)
}

func (s *ServiceService) GetAll(filter *ports.ServiceFilter) ([]*domain.Service, error) {
	return s.serviceRepo.FindAll(filter)
}

func (s *ServiceService) GetByID(id uint64) (*domain.Service, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	service, err := s.serviceRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("service not found")
	}

	return service, nil
}

func (s *ServiceService) GetByLineCode(lineCode string) ([]*domain.Service, error) {
	if lineCode == "" {
		return nil, errors.New("line code is required")
	}

	services, err := s.serviceRepo.FindByLineCode(lineCode)
	if err != nil {
		return nil, err
	}

	return services, nil
}

func (s *ServiceService) Update(id uint64, name, port, targetPort, serviceType, clusterIp, ExternalIp string, isActive bool, namespaceID, deploymentID, statusID uint64) (*domain.Service, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if port == "" {
		return nil, errors.New("port is required")
	}
	if targetPort == "" {
		return nil, errors.New("target port is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}
	if deploymentID == 0 {
		return nil, errors.New("deployment ID is required")
	}

	if statusID == 0 {
		statusID = 1
	}

	service, err := s.serviceRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("service not found")
	}

	_, err = s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	service.Name = name
	service.Port = port
	service.TargetPort = targetPort
	service.Type = serviceType
	service.ClusterIP = clusterIp
	service.ExternalIP = ExternalIp
	service.IsActive = isActive
	service.NamespaceID = namespaceID
	service.DeploymentID = deploymentID
	service.StatusID = statusID

	if err := s.serviceRepo.Update(service); err != nil {
		return nil, err
	}

	return s.serviceRepo.FindByID(id)
}

func (s *ServiceService) UpdateStatus(id uint64, statusID uint64) (*domain.Service, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}
	if statusID == 0 {
		return nil, errors.New("status ID is required")
	}

	// Verify service exists
	service, err := s.serviceRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("service not found")
	}

	// Update only the status
	service.StatusID = statusID

	if err := s.serviceRepo.Update(service); err != nil {
		return nil, err
	}

	return service, nil
}

func (s *ServiceService) UpdateActiveStatus(id uint64, isActive bool) (*domain.Service, error) {
	if id == 0 {
		return nil, errors.New("id is required")
	}

	// Verify service exists
	service, err := s.serviceRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("service not found")
	}

	// Update only the active status
	service.IsActive = isActive

	if err := s.serviceRepo.Update(service); err != nil {
		return nil, err
	}

	return service, nil
}

func (s *ServiceService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	_, err := s.serviceRepo.FindByID(id)
	if err != nil {
		return errors.New("service not found")
	}

	return s.serviceRepo.Delete(id)
}
