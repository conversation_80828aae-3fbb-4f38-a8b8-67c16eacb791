package services

import (
	"errors"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
)

type SwitchDnsService struct {
	namespaceService ports.NamespaceService
	poolService      ports.PoolService
	dnsService       ports.DnsService
	operationService ports.OperationService
	domainService    ports.DomainService
}

func NewSwitchDnsService(namespaceService ports.NamespaceService, poolService ports.PoolService, dnsService ports.DnsService, operationService ports.OperationService, domainService ports.DomainService) *SwitchDnsService {
	return &SwitchDnsService{
		namespaceService: namespaceService,
		poolService:      poolService,
		dnsService:       dnsService,
		operationService: operationService,
		domainService:    domainService,
	}
}

func (s *SwitchDnsService) SwitchDns(userID uint64, accessToken string, namespaceID uint64) (interface{}, error) {
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}
	namespace, err := s.namespaceService.GetByID(namespaceID)
	if err != nil {
		return nil, err
	}
	currentDefaultDomain, err := s.domainService.GetDefaultByNamespaceID(namespaceID)
	if err != nil {
		return nil, err
	}
	masterDomain := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME")
	isCurrentMaster := currentDefaultDomain.Name == masterDomain

	if isCurrentMaster {
		var req dto.OperationCreateReq
		req = dto.OperationCreateReq{
			ClusterID:   namespace.ClusterID,
			NamespaceID: namespaceID,
			Method:      "apply",
		}
		_, err = s.operationService.CreateOperationAsync(userID, accessToken, req)
		if err != nil {
			return nil, err
		}
	} else {
		var req dto.HandleDnsRequest
		req = dto.HandleDnsRequest{
			NamespaceID: namespaceID,
			ZoneID:      currentDefaultDomain.ZoneID,
			Method:      "apply",
		}
		_, err = s.dnsService.HandleDnsAsync(accessToken, req)
		if err != nil {
			return nil, err
		}
	}

	return "Switch DNS started.", nil
}
