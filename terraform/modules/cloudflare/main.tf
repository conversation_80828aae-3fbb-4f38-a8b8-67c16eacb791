terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

resource "cloudflare_record" "record" {
  count = length(var.sub_domain_list)
  zone_id = var.cloudflare_zone_id
  name    = var.sub_domain_list[count.index].name
  value   = var.sub_domain_list[count.index].is_active == true ? var.ip_address : "google.com"
  type    = var.sub_domain_list[count.index].is_active == true ? var.type : "CNAME"
  proxied = true

  # Add explicit timeouts (delete timeout not supported for cloudflare_record)
  timeouts {
    create = "5m"
    update = "5m"
  }
}