variable "cloudflare_api_token" {
  type = string
  default = ""
  description = "Cloudflare API token for authentication"
}

variable "cloudflare_zone_id" {
  type = string
  default = ""
  description = "Cloudflare zone ID where DNS records will be created"
}

variable "ip_address" {
  type = string
  default = ""
  description = "IP address for A records"
}

variable "type" {
  type = string
  default = "A"
  description = "DNS record type (A, CNAME, etc.)"
}

variable "sub_domain_list" {
  type = list(object({
    name      = string
    is_active = bool
  }))
  default = [
    {
      name      = "@"
      is_active = true
    }
  ]
  description = "List of subdomains to create DNS records for"
}
